import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import Overlay from 'react-bootstrap/Overlay'
import { useSelector } from 'react-redux'
import { I18n } from 'react-redux-i18n'

import dayjs from 'dayjs'
import cloneDeep from 'lodash/cloneDeep'
import forEach from 'lodash/forEach'
import isEmpty from 'lodash/isEmpty'
import { RootState } from 'store/reducers'
import styled from 'styled-components'
import { theme } from 'styles/theme'

import CalendarPopover from './components/CalendarPopover'
import HoursTable from './components/HoursTable'
import RollingNumber from './components/RollingNumber'
import { usePeriod } from 'contexts/PeriodContext'

import { database, useAppContext } from '../../../index'
import PayrollConflictModal from '../../PayrollOld/modals/PayrollConflictModal'
import { matchClockInWithScheduledShift } from '../../PayrollOld/payrollUtils'

import {
  getCurrentUserInfo,
  logShiftChanges,
  logShiftDeletion
} from 'utils/payroll/activityLogger'
import {
  RoleFilterState,
  clearRoleFilterState,
  createInitialFilterState,
  extractRolesFromCompany,
  getSingleRoleInfo,
  isSingleBOHRole,
  loadRoleFilterState,
  saveRoleFilterState
} from 'utils/payroll/roleFilterUtils'
import {
  analyzeShiftConflicts
} from 'utils/payroll/shiftConflictUtils'

import {
  AttendanceSettings,
  AttendanceShift,
  AttendanceShifts,
  IAllConflictingAttendanceShifts
} from 'types/attendance'
import { Company, IPosition } from 'types/company'
import { IEmployee } from 'types/employee'
import { SalaryRateUnit } from 'utils/constants'
import firebase from 'firebase/app'

import calendarIcon from 'img/icons/calendarBlankIcon.svg'
import ArrowLeft from 'img/IconsHover/ArrowLeftGrey'
import ArrowRight from 'img/IconsHover/ArrowRightGrey'
import { ReactComponent as ClockIcon } from 'img/icons/hoursIcon.svg'
import { ReactComponent as SparkleIcon } from 'img/icons/sparkleIcon.svg'

// import noteIcon from 'img/icons/noteIcon.svg'

interface HoursProps {
  employeesArray: IEmployee[]
  employeesByRole: {
    [roleId: string]: { role: IPosition; employees: IEmployee[] }
  }
  searchTerm: string
  setSearchTerm: (value: string) => void
  selectedPositionId: string
  setSelectedPositionId: (value: string) => void
  currentCompany: Company
  displayBy: string
  setDisplayBy: (value: string) => void
  displayByArray: Array<{ id: string; label: string; icon: React.ReactNode }>
  onSearchEmployee: (value: string) => void
  attendanceSettings: AttendanceSettings
  setAttendanceSettings: (settings: AttendanceSettings) => void
  hasPayrollIntegration: boolean
}

const Hours: React.FC<HoursProps> = ({
  employeesArray,
  employeesByRole,
  searchTerm,
  setSearchTerm: _setSearchTerm,
  selectedPositionId,
  setSelectedPositionId: _setSelectedPositionId,
  currentCompany,
  displayBy,
  setDisplayBy,
  displayByArray,
  onSearchEmployee,
  attendanceSettings,
  setAttendanceSettings: _setAttendanceSettings,
  hasPayrollIntegration
}) => {
  // Use period context for all period-related data
  const { period, actions } = usePeriod()
  const {
    basePeriodStart: startOfPeriod,
    payrollLength,
    currentPeriodOffset,
    currentPeriodStart,
    currentPeriodEnd,
    periodDisplay,
    startOfPeriodStr,
    endOfPeriodStr
  } = period

  // Attendance data state (will be loaded based on period context)
  const [attendanceData, setAttendanceData] = useState<AttendanceShifts>({})
  const [isDataLoaded, setIsDataLoaded] = useState(false)
  const [schedule, setSchedule] = useState<any>({}) // Schedule data for conflict detection
  const [isScheduleLoaded, setIsScheduleLoaded] = useState(false)

  // Error handling and loading states
  const [dataError, setDataError] = useState<string | null>(null)
  const [scheduleError, setScheduleError] = useState<string | null>(null)
  const [isRetrying, setIsRetrying] = useState(false)
  const [retryCount, setRetryCount] = useState(0)
  const [isOnline, setIsOnline] = useState(navigator.onLine)
  const maxRetries = 3

  // Network connectivity monitoring
  useEffect(() => {
    const handleOnline = () => {
      setIsOnline(true)
      // Auto-retry when connection is restored
      if ((dataError || scheduleError) && !isRetrying) {
        handleRetry()
      }
    }
    const handleOffline = () => setIsOnline(false)

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [dataError, scheduleError, isRetrying])

  // Auto-retry when user returns to the page (visibility API)
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden && (dataError || scheduleError) && !isRetrying && isOnline) {
        // User returned to the page and we have errors - auto retry
        setTimeout(() => {
          if ((dataError || scheduleError) && !isRetrying) {
            console.log('Auto-retrying due to page visibility change')
            handleRetry()
          }
        }, 1000) // Small delay to avoid immediate retry
      }
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange)
  }, [dataError, scheduleError, isRetrying, isOnline])
  const { user, currentEmployee } = useAppContext()

  // ========================================
  // TEST DATA GENERATION FOR SHIFTPOPOVER CONFLICT ANALYSIS
  // ========================================
  // This code generates test data in the database for testing the ShiftPopover conflict analysis
  // Remove this section manually when testing is complete

  const [testDataGenerated, setTestDataGenerated] = useState(false)

  useEffect(() => {
    const generateTestData = async () => {
      if (!currentCompany?.key || !employeesArray?.length || testDataGenerated) {
        if (testDataGenerated) {
          console.log('⏭️ Test data already generated, skipping...')
        }
        return
      }

      console.log('🧪 Generating test data for ShiftPopover conflict analysis...')

      // Use the current payroll period dates instead of today/tomorrow
      const today = startOfPeriodStr // First day of current payroll period
      const tomorrow = dayjs(startOfPeriodStr).add(1, 'day').format('YYYY-MM-DD')

      console.log('📅 Using payroll period dates:')
      console.log('  Today (period start):', today)
      console.log('  Tomorrow:', tomorrow)
      console.log('  Period range:', startOfPeriodStr, 'to', endOfPeriodStr)

      // Find actual employee IDs by name from the current employees array
      const findEmployeeByName = (firstName: string, lastName?: string) => {
        return employeesArray.find((emp: IEmployee) => {
          const nameMatch = emp.name?.toLowerCase() === firstName.toLowerCase()
          if (lastName) {
            return nameMatch && emp.surname?.toLowerCase() === lastName.toLowerCase()
          }
          return nameMatch
        })
      }

      const alekEmployee = findEmployeeByName('Alek', 'Riendeau')
      const alexandraEmployee = findEmployeeByName('Alexandra1', 'Sylvestre')
      const alphonseEmployee = findEmployeeByName('Alphonse', 'Monpetit')
      const aminaEmployee = findEmployeeByName('Amina', 'Goudjil')
      const andreiEmployee = findEmployeeByName('Andrei', 'Moldovan')

      // Log found employees
      console.log('📋 Found employees:')
      console.log('  Alek Riendeau:', alekEmployee?.uid || 'NOT FOUND')
      console.log('  Alexandra1 Sylvestre:', alexandraEmployee?.uid || 'NOT FOUND')
      console.log('  Alphonse Monpetit:', alphonseEmployee?.uid || 'NOT FOUND')
      console.log('  Amina Goudjil:', aminaEmployee?.uid || 'NOT FOUND')
      console.log('  Andrei Moldovan:', andreiEmployee?.uid || 'NOT FOUND')

      // Only proceed if we found at least some employees
      const foundEmployees = [alekEmployee, alexandraEmployee, alphonseEmployee, aminaEmployee, andreiEmployee].filter(Boolean)
      if (foundEmployees.length === 0) {
        console.warn('⚠️ No matching employees found. Please check employee names in your database.')
        return
      }

      // Use actual position IDs from your company's jobs
      const companyPositions = Object.keys(currentCompany.jobs || {})
      const TEST_POSITIONS = companyPositions.length > 0 ? companyPositions.slice(0, 4) : [
        'test-position-server',
        'test-position-cook',
        'test-position-manager',
        'test-position-host'
      ]

      console.log('🏢 Using company positions:', TEST_POSITIONS)

      const createShift = (options: {
        start?: number
        end?: number
        positionId?: string
        rate?: number
        isConfirmed?: boolean
        scheduledStart?: number
        scheduledEnd?: number
      }): AttendanceShift => {
        const shift: AttendanceShift = {
          start: options.start,
          end: options.end,
          positionId: options.positionId,
          rate: options.rate || 15.00,
          type: SalaryRateUnit.SALARY_TYPE_HOURLY,
          isConfirmed: options.isConfirmed || false,
          manuallyCreated: true,
          breaks: {},
          clockInSource: 'manual',
          clockOutSource: options.end ? 'manual' : undefined
        }

        // Add scheduled shift data if provided
        if (options.scheduledStart && options.scheduledEnd) {
          (shift as any).scheduledShift = {
            start: options.scheduledStart,
            end: options.scheduledEnd,
            uid: 'scheduled-shift-' + Math.random().toString(36).substr(2, 9),
            positionId: options.positionId || '',
            subcategoryId: 'test-subcategory'
          }
        }

        return shift
      }

      const testData: AttendanceShifts = {}
      testData[today] = {}
      testData[tomorrow] = {}

      // SCENARIO 1: Overlapping shifts (RED conflicts) - Alek Riendeau
      if (alekEmployee) {
        testData[today][alekEmployee.uid] = {
          'shift-1': createShift({
            start: 480, // 8:00 AM
            end: 720,   // 12:00 PM
            positionId: TEST_POSITIONS[0],
            rate: 15.50,
            isConfirmed: false
          }),
          'shift-2': createShift({
            start: 660, // 11:00 AM (overlaps with shift-1)
            end: 900,   // 3:00 PM
            positionId: TEST_POSITIONS[1],
            rate: 16.00,
            isConfirmed: false
          })
        }
      }

      // SCENARIO 2: Early arrival (ORANGE conflicts) - Alexandra1 Sylvestre
      if (alexandraEmployee) {
        testData[today][alexandraEmployee.uid] = {
          'shift-1': createShift({
            start: 450, // 7:30 AM (30 min early)
            end: 720,   // 12:00 PM
            positionId: TEST_POSITIONS[0],
            rate: 15.50,
            isConfirmed: false,
            scheduledStart: 480, // Was scheduled for 8:00 AM
            scheduledEnd: 720
          })
        }
      }

      // SCENARIO 3: Missing role selection (GREY conflicts) - Alphonse Monpetit
      if (alphonseEmployee) {
        testData[today][alphonseEmployee.uid] = {
          'shift-1': createShift({
            start: 480, // 8:00 AM
            end: 720,   // 12:00 PM
            positionId: undefined, // Missing role!
            rate: 15.50,
            isConfirmed: false
          }),
          'shift-2': createShift({
            start: 780, // 1:00 PM
            end: 1020,  // 5:00 PM
            positionId: TEST_POSITIONS[1],
            rate: 16.00,
            isConfirmed: false
          })
        }
      }

      // SCENARIO 4: Missing clock out (ORANGE conflicts) - Amina Goudjil
      if (aminaEmployee) {
        testData[today][aminaEmployee.uid] = {
          'shift-1': createShift({
            start: 480, // 8:00 AM
            end: undefined, // Missing clock out!
            positionId: TEST_POSITIONS[2],
            rate: 18.00,
            isConfirmed: false
          })
        }
      }

      // SCENARIO 5: Short shift (ORANGE conflicts) - Andrei Moldovan
      if (andreiEmployee) {
        testData[today][andreiEmployee.uid] = {
          'shift-1': createShift({
            start: 480, // 8:00 AM
            end: 600,   // 10:00 AM (only 2 hours)
            positionId: TEST_POSITIONS[0],
            rate: 15.50,
            isConfirmed: false
          })
        }
      }

      // SCENARIO 6: Perfect shifts (GREEN status) - Alphonse Monpetit tomorrow
      if (alphonseEmployee) {
        testData[tomorrow][alphonseEmployee.uid] = {
          'shift-1': createShift({
            start: 480, // 8:00 AM
            end: 720,   // 12:00 PM
            positionId: TEST_POSITIONS[0],
            rate: 15.50,
            isConfirmed: false
          }),
          'shift-2': createShift({
            start: 780, // 1:00 PM
            end: 1020,  // 5:00 PM
            positionId: TEST_POSITIONS[1],
            rate: 16.00,
            isConfirmed: false
          })
        }
      }

      try {
        const updates: { [path: string]: any } = {}

        // Prepare Firebase updates for attendance data
        Object.entries(testData).forEach(([date, employeeData]) => {
          Object.entries(employeeData).forEach(([employeeId, shifts]) => {
            Object.entries(shifts).forEach(([shiftKey, shiftData]) => {
              const path = `Attendance/${currentCompany.key}/${date}/${employeeId}/${shiftKey}`
              updates[path] = shiftData
              console.log(`📝 Preparing update: ${path}`)
            })
          })
        })

        console.log(`🔄 Updating ${Object.keys(updates).length} paths in Firebase...`)

        // Save to Firebase with timeout
        console.log('🔥 Starting Firebase update...')
        const updatePromise = firebase.database().ref().update(updates)
        const timeoutPromise = new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Firebase update timeout')), 10000)
        )

        await Promise.race([updatePromise, timeoutPromise])
        console.log('💾 Firebase update completed successfully!')

        console.log('✅ Test attendance data created successfully!')
        console.log('📊 Created test scenarios:')
        if (alekEmployee) console.log('  🔴 Overlapping shifts (Alek Riendeau)')
        if (alexandraEmployee) console.log('  🟠 Early arrivals (Alexandra1 Sylvestre)')
        if (alphonseEmployee) console.log('  ⚫ Missing roles (Alphonse Monpetit)')
        if (aminaEmployee) console.log('  🟠 Missing clock outs (Amina Goudjil)')
        if (andreiEmployee) console.log('  🟠 Short shifts (Andrei Moldovan)')
        if (alphonseEmployee) console.log('  🟢 Perfect shifts (Alphonse Monpetit tomorrow)')
        console.log('📍 Navigate to different dates to see all scenarios')
        console.log('🗑️ Remove this test data generation code when testing is complete')

        // Mark test data as generated to prevent duplicate runs
        setTestDataGenerated(true)

      } catch (error) {
        console.error('❌ Failed to create test data:', error)
      }
    }

    // Only generate test data once when component mounts
    generateTestData()
  }, [currentCompany?.key, employeesArray, startOfPeriodStr, endOfPeriodStr, testDataGenerated]) // Run when company, employees, or period changes

  // END TEST DATA GENERATION
  // ========================================

  // Retry utility function with exponential backoff
  const retryWithBackoff = useCallback(async (
    operation: () => Promise<void>,
    attempt: number = 1
  ): Promise<void> => {
    try {
      await operation()
    } catch (error) {
      if (attempt < maxRetries) {
        const delay = Math.min(1000 * Math.pow(2, attempt - 1), 10000) // Max 10 seconds
        console.warn(`Attempt ${attempt} failed, retrying in ${delay}ms...`, error)

        setIsRetrying(true)
        setRetryCount(attempt)

        await new Promise(resolve => setTimeout(resolve, delay))
        return retryWithBackoff(operation, attempt + 1)
      } else {
        setIsRetrying(false)
        setRetryCount(0)
        throw error
      }
    }
  }, [maxRetries, setIsRetrying, setRetryCount])

  // Load attendance data and schedule data when period changes
  useEffect(() => {
    let attendanceRef: ReturnType<typeof database.ref> | null = null
    let scheduleRef: ReturnType<typeof database.ref> | null = null
    let isMounted = true

    const loadAttendanceData = (): Promise<void> => {
      return new Promise((resolve, reject) => {
        try {
          const attendanceQuery = database
            .ref('Attendance/' + currentCompany.key)
            .orderByKey()
            .startAt(startOfPeriodStr)
            .endAt(endOfPeriodStr)

          attendanceRef = database.ref('Attendance/' + currentCompany.key)

          const onValue = (snapshot: any) => {
            if (!isMounted) return

            try {
              const data = snapshot.val() || {}
              setAttendanceData(data)
              setIsDataLoaded(true)
              setDataError(null)
              resolve()
            } catch (error) {
              console.error('Error processing attendance data:', error)
              reject(new Error('Failed to process attendance data'))
            }
          }

          const onError = (error: any) => {
            console.error('Firebase attendance error:', error)
            reject(new Error(`Firebase attendance error: ${error.message || 'Unknown error'}`))
          }

          attendanceQuery.on('value', onValue, onError)
        } catch (error) {
          console.error('Error setting up attendance listener:', error)
          reject(new Error('Failed to setup attendance data listener'))
        }
      })
    }

    const loadScheduleData = (): Promise<void> => {
      return new Promise((resolve, reject) => {
        try {
          const scheduleQuery = database
            .ref('WeeklySchedule/' + currentCompany.key)
            .orderByKey()
            .startAt(startOfPeriodStr)
            .endAt(endOfPeriodStr)

          scheduleRef = database.ref('WeeklySchedule/' + currentCompany.key)

          const onValue = (snapshot: any) => {
            if (!isMounted) return

            try {
              const data = snapshot.val() || {}
              setSchedule(data)
              setIsScheduleLoaded(true)
              setScheduleError(null)
              resolve()
            } catch (error) {
              console.error('Error processing schedule data:', error)
              reject(new Error('Failed to process schedule data'))
            }
          }

          const onError = (error: any) => {
            console.error('Firebase schedule error:', error)
            reject(new Error(`Firebase schedule error: ${error.message || 'Unknown error'}`))
          }

          scheduleQuery.on('value', onValue, onError)
        } catch (error) {
          console.error('Error setting up schedule listener:', error)
          reject(new Error('Failed to setup schedule data listener'))
        }
      })
    }

    const loadData = async () => {
      if (!currentCompany.key) return

      try {
        // Reset error states
        setDataError(null)
        setScheduleError(null)
        setIsRetrying(false)
        setRetryCount(0)

        // Load both attendance and schedule data with retry logic
        await Promise.all([
          retryWithBackoff(loadAttendanceData).catch(error => {
            console.error('Failed to load attendance data after retries:', error)
            setDataError(error.message || 'Failed to load attendance data')
          }),
          retryWithBackoff(loadScheduleData).catch(error => {
            console.error('Failed to load schedule data after retries:', error)
            setScheduleError(error.message || 'Failed to load schedule data')
          })
        ])
      } catch (error) {
        console.error('Unexpected error during data loading:', error)
      } finally {
        setIsRetrying(false)
        setRetryCount(0)
      }
    }

    loadData()

    return () => {
      isMounted = false
      if (attendanceRef) {
        attendanceRef.off()
      }
      if (scheduleRef) {
        scheduleRef.off()
      }
      setIsScheduleLoaded(false)
      setIsDataLoaded(false)
      setDataError(null)
      setScheduleError(null)
      setIsRetrying(false)
      setRetryCount(0)
    }
  }, [currentCompany.key, startOfPeriodStr, endOfPeriodStr,retryWithBackoff])

  // Role filtering state and logic
  const departmentRoles = useMemo(
    () => extractRolesFromCompany(currentCompany.jobs || {}),
    [currentCompany.jobs]
  )

  const isSingleRole = useMemo(
    () => isSingleBOHRole(departmentRoles),
    [departmentRoles]
  )

  const singleRoleInfo = useMemo(
    () => getSingleRoleInfo(departmentRoles),
    [departmentRoles]
  )

  const [roleFilterState, setRoleFilterState] = useState<RoleFilterState>(
    () => {
      // Initialize with empty state first, will be properly set in useEffect
      return {
        selectedDepartments: [],
        selectedRoles: {},
        selectedSubcategories: {}
      }
    }
  )

  // Initialize filter state when department roles are available
  useEffect(() => {
    // Only proceed if we have department roles data
    if (
      departmentRoles.FOH.length === 0 &&
      departmentRoles.BOH.length === 0 &&
      departmentRoles.MNG.length === 0
    ) {
      return
    }

    // Clear any potentially corrupted saved state first
    clearRoleFilterState()

    const defaultState = createInitialFilterState(departmentRoles)
    setRoleFilterState(defaultState)
  }, [departmentRoles])

  // Handle view switching and saved state
  useEffect(() => {
    if (displayBy !== 'roles') {
      // Reset to default (all roles selected) when switching to "By Employee"
      clearRoleFilterState()
      if (
        departmentRoles.FOH.length > 0 ||
        departmentRoles.BOH.length > 0 ||
        departmentRoles.MNG.length > 0
      ) {
        const defaultState = createInitialFilterState(departmentRoles)
        setRoleFilterState(defaultState)
      }
    } else {
      // Load saved state when switching to "By Roles" - only run once when switching to roles view
      const saved = loadRoleFilterState()
      if (saved && saved.selectedDepartments.length > 0) {
        setRoleFilterState(saved)
      } else if (
        departmentRoles.FOH.length > 0 ||
        departmentRoles.BOH.length > 0 ||
        departmentRoles.MNG.length > 0
      ) {
        // Fallback to default state if no saved state
        const defaultState = createInitialFilterState(departmentRoles)
        setRoleFilterState(defaultState)
      }
    }
  }, [displayBy, departmentRoles]) // Removed roleFilterState.selectedDepartments.length from dependencies

  const handleRoleFilterChange = (newState: RoleFilterState) => {
    // Always update the local state immediately
    setRoleFilterState(newState)

    // Save to localStorage when in roles view
    if (displayBy === 'roles') {
      saveRoleFilterState(newState)
    }
  }

  const weekPeriodTabs = [
    { id: 'biweekly', label: I18n.t('payroll.biweekly') },
    { id: 'week1', label: I18n.t('payroll.week') + ' 1' },
    { id: 'week2', label: I18n.t('payroll.week') + ' 2' }
  ]
  const [activeWeekPeriodTab, setActiveWeekPeriodTab] = useState('biweekly')

  const isLocaleFr =
    useSelector((state: RootState) => state.i18n.locale) === 'fr'

  // Use period display from context, adjusted for active week tab
  const currentDisplayPeriod = React.useMemo(() => {
    // Calculate effective period based on active week tab
    let effectiveStart = currentPeriodStart
    let effectiveEnd = currentPeriodEnd

    if (activeWeekPeriodTab === 'week1') {
      // Week 1: First 7 days of the bi-weekly period
      effectiveStart = currentPeriodStart
      effectiveEnd = currentPeriodStart.clone().add(6, 'days')
    } else if (activeWeekPeriodTab === 'week2') {
      // Week 2: Second 7 days of the bi-weekly period
      effectiveStart = currentPeriodStart.clone().add(7, 'days')
      effectiveEnd = currentPeriodStart.clone().add(13, 'days')
    }
    // For 'biweekly', use the full period (no changes needed)

    return {
      start: effectiveStart,
      end: effectiveEnd,
      startStr: effectiveStart.format(isLocaleFr ? 'DD MMM' : 'MMM DD'),
      endStr: effectiveEnd.format(isLocaleFr ? 'DD MMM' : 'MMM DD'),
      isCurrentPeriod: periodDisplay.isCurrentPeriod,
      isPastPeriod: periodDisplay.isPastPeriod,
      isFuturePeriod: periodDisplay.isFuturePeriod
    }
  }, [currentPeriodStart, currentPeriodEnd, activeWeekPeriodTab, isLocaleFr, periodDisplay])

  const isPastPeriod = currentDisplayPeriod.isPastPeriod
  const isFuturePeriod = currentDisplayPeriod.isFuturePeriod

  // Handle period selection from CalendarPopover
  const handlePeriodSelect = (offset: number) => {
    // Update the user period offset in context
    actions.setUserPeriodOffset(offset)
  }

  // Calculate filtered period data based on active week tab
  const filteredPeriodData = useMemo(() => {
    // Use fixed startOfPeriod + offset approach
    const basePeriodStart = startOfPeriod
      .clone()
      .add(currentPeriodOffset * payrollLength, 'days')

    let filteredStartOfPeriod = basePeriodStart
    let filteredPayrollLength = payrollLength

    if (activeWeekPeriodTab === 'week1') {
      // First week of the biweekly period
      filteredPayrollLength = 7
    } else if (activeWeekPeriodTab === 'week2') {
      // Second week of the biweekly period
      filteredStartOfPeriod = basePeriodStart.clone().add(7, 'days')
      filteredPayrollLength = 7
    }
    // 'biweekly' uses the full period (no changes needed)

    return {
      startOfPeriod: filteredStartOfPeriod,
      payrollLength: filteredPayrollLength
    }
  }, [startOfPeriod, payrollLength, currentPeriodOffset, activeWeekPeriodTab])


  const staticWeekDays = useMemo(() => {
    // Monday = 1, Sunday = 7
    const weekKeys = [
      'jobs.Monday',
      'jobs.Tuesday',
      'jobs.Wednesday',
      'jobs.Thursday',
      'jobs.Friday',
      'jobs.Saturday',
      'jobs.Sunday'
    ]
    const weekStartingDay = currentCompany.payrollStartingDay || 'Monday'
    const startingIndex = weekKeys.indexOf('jobs.' + weekStartingDay)
    weekKeys.push(...weekKeys.splice(0, startingIndex))
    return weekKeys.map(key => {
      const dayName = I18n.t(key)
      return {
        dayName,
        displayText: dayName
      }
    })
  }, [currentCompany.payrollStartingDay])
  
  const currentDayIndex = useMemo( () =>
    {
    const today = dayjs().day(); // 0 = Sunday, 1 = Monday, ..., 6 = Saturday
    const weekDayNames = [ 'Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday' ]
    const getDayIndexInStaticWeekDay= (dayName: string) => {
      const index = staticWeekDays.findIndex(day => day.dayName === dayName)
      return index
    }
    return getDayIndexInStaticWeekDay( weekDayNames[ today ] );
          
    }, [staticWeekDays])
  // Filter attendance data based on the selected week tab
  const filteredAttendanceData = useMemo(() => {
    if (activeWeekPeriodTab === 'biweekly') {
      return attendanceData // Return all data for biweekly view
    }

    const filtered: AttendanceShifts = {}
    const { startOfPeriod: periodStart, payrollLength: periodLength } =
      filteredPeriodData

    // Generate date keys for the filtered period
    for (let i = 0; i < periodLength; i++) {
      const dateKey = periodStart.clone().add(i, 'days').format('YYYY-MM-DD')
      if (attendanceData[dateKey]) {
        filtered[dateKey] = attendanceData[dateKey]
      }
    }

    return filtered
  }, [attendanceData, activeWeekPeriodTab, filteredPeriodData])

  // Helper function to calculate total salary for a given period
  const calculatePeriodSalary = React.useCallback(
    (periodStart: dayjs.Dayjs, periodEnd: dayjs.Dayjs) => {
      let totalSalary = 0

      // Iterate through each day in the period
      let currentDay = periodStart.clone()
      while (currentDay.isSameOrBefore(periodEnd)) {
        const dateKey = currentDay.format('YYYY-MM-DD')
        const dayShifts = attendanceData[dateKey]

        if (dayShifts) {
          // Calculate salary for all employees on this day
          for (const employee of employeesArray) {
            const employeeShifts = dayShifts[employee.uid]
            if (employeeShifts) {
              for (const shift of Object.values(employeeShifts)) {
                totalSalary +=
                  (shift as AttendanceShift & { salary?: number }).salary || 0
              }
            }
          }
        }

        currentDay = currentDay.add(1, 'day')
      }

      return totalSalary
    },
    [attendanceData, employeesArray]
  )

  // Calculate salary savings: current period vs previous period
  const salarySavings = useMemo(() => {
    // For current or future periods, always use current vs previous comparison
    const isCurrentOrFuture = currentPeriodOffset >= 0

    let displayedPeriodSalary = 0
    let comparisonPeriodSalary = 0

    if (isCurrentOrFuture) {
      // For current/future periods: compare current period with previous period
      // Calculate current period (offset 0) salary
      const actualCurrentStart = startOfPeriod.clone()
      const actualCurrentEnd = actualCurrentStart
        .clone()
        .add(payrollLength - 1, 'days')

      // Calculate previous period (offset -1) salary
      const actualPreviousStart = startOfPeriod
        .clone()
        .add(-1 * payrollLength, 'days')
      const actualPreviousEnd = actualPreviousStart
        .clone()
        .add(payrollLength - 1, 'days')

      displayedPeriodSalary = calculatePeriodSalary(
        actualCurrentStart,
        actualCurrentEnd
      )
      comparisonPeriodSalary = calculatePeriodSalary(
        actualPreviousStart,
        actualPreviousEnd
      )
    } else {
      // For past periods: compare selected period with its previous period
      const currentPeriodStart = startOfPeriod
        .clone()
        .add(currentPeriodOffset * payrollLength, 'days')
      const currentPeriodEnd = currentPeriodStart
        .clone()
        .add(payrollLength - 1, 'days')

      const previousPeriodStart = startOfPeriod
        .clone()
        .add((currentPeriodOffset - 1) * payrollLength, 'days')
      const previousPeriodEnd = previousPeriodStart
        .clone()
        .add(payrollLength - 1, 'days')

      displayedPeriodSalary = calculatePeriodSalary(
        currentPeriodStart,
        currentPeriodEnd
      )
      comparisonPeriodSalary = calculatePeriodSalary(
        previousPeriodStart,
        previousPeriodEnd
      )
    }

    const savings = comparisonPeriodSalary - displayedPeriodSalary

    return Math.max(0, savings) // Only show positive savings
  }, [startOfPeriod, currentPeriodOffset, payrollLength, calculatePeriodSalary])

  // Calculate conflicting shifts for the current period
  const allConflictingShifts = useMemo(() => {
    const conflicts: IAllConflictingAttendanceShifts = {}
    const currentPeriodStart = startOfPeriod
      .clone()
      .add(currentPeriodOffset * payrollLength, 'days')
    const currentPeriodEnd = currentPeriodStart
      .clone()
      .add(payrollLength - 1, 'days')

    // Iterate through each day in the current period
    let currentDay = currentPeriodStart.clone()

    while (currentDay.isSameOrBefore(currentPeriodEnd)) {
      const dateKey = currentDay.format('YYYY-MM-DD')
      const dayShifts = attendanceData[dateKey]

      if (dayShifts) {
        const currentDayCopy = currentDay.clone()
        forEach(dayShifts, (employeeShifts, employeeId) => {
          forEach(employeeShifts, (shift, shiftKey) => {
            if (!shift.isConfirmed) {
              // Apply rounding to shift times (matching PayrollOld logic)
              const roundingTime = attendanceSettings?.roundingTime || 15
              const shiftStartRounded = shift.start ? Math.round(shift.start / roundingTime) * roundingTime : 0
              const shiftEndRounded = shift.end ? Math.round(shift.end / roundingTime) * roundingTime : null

              // Find matching scheduled shift using actual schedule data
              const scheduledPositions = schedule?.[dateKey]?.[employeeId] || {}
              let scheduledShift = null

              if (!isEmpty(scheduledPositions) && shift.start !== undefined) {
                const dayShifts: any[] = []

                forEach(scheduledPositions, (subpositions, positionId) => {
                  forEach(subpositions, (subpositionShifts, subcategoryId) => {
                    forEach(subpositionShifts, (scheduledShiftData, shiftKey) => {
                      dayShifts.push({
                        ...scheduledShiftData,
                        positionId,
                        subcategoryId,
                        shiftKey
                      })
                    })
                  })
                })

                const matchedShift = matchClockInWithScheduledShift({
                  dayShifts,
                  shiftStartRounded,
                  defaultDuration: currentCompany?.defaultDuration || 480,
                  shift: {
                    start: shiftStartRounded,
                    end: shiftEndRounded
                  }
                })

                if (matchedShift) {
                  scheduledShift = matchedShift
                }
              }

              // Analyze shift conflicts
              const conflictAnalysis = analyzeShiftConflicts(
                shift,
                shiftStartRounded,
                shiftEndRounded,
                scheduledShift,
                currentDayCopy.isSame(dayjs(), 'day'),
                false // TODO: Calculate actual working status
              )

              if (conflictAnalysis.isConflictingShift) {
                if (!conflicts[dateKey]) {
                  conflicts[dateKey] = {}
                }
                if (!conflicts[dateKey][employeeId]) {
                  conflicts[dateKey][employeeId] = {}
                }

                conflicts[dateKey][employeeId][shiftKey] = {
                  ...shift,
                  neverClockedOut: conflictAnalysis.notClockedOut,
                  scheduledShift,
                  missingPosition: !shift.positionId,
                  isClockInDifferent: conflictAnalysis.isClockInDifferent,
                  isClockOutDiffrent: conflictAnalysis.isClockOutDifferent,
                  shiftStartRounded,
                  shiftEndRounded: shiftEndRounded || 0
                }
              }
            }
          })
        })
      }

      currentDay = currentDay.add(1, 'day')
    }

    return conflicts
  }, [
    attendanceData,
    schedule,
    startOfPeriod,
    currentPeriodOffset,
    payrollLength,
    currentCompany,
    attendanceSettings?.roundingTime
  ])

  // Count total number of conflicts
  const numberOfConflicts = useMemo(() => {
    let count = 0
    forEach(allConflictingShifts, dayShifts => {
      forEach(dayShifts, employeeShifts => {
        count += Object.keys(employeeShifts).length
      })
    })
    return count
  }, [allConflictingShifts])

  const [showCalendar, setShowCalendar] = useState(false)
  const calendarRef = useRef<HTMLDivElement>(null)
  const [showConflictModal, setShowConflictModal] = useState(false)

  // Save shift function (ported from PayrollOld)
  const onSave = async (
    newShift: { [key: string]: AttendanceShift },
    employeeId: string,
    date: string
  ) => {
    const copy = cloneDeep(attendanceData)

    if (!copy[date]) {
      copy[date] = {}
    }

    if (!copy[date][employeeId]) {
      copy[date][employeeId] = {}
    }

    // Get old shift data for activity logging
    const oldShifts = copy[date][employeeId] || {}

    // Update the shifts
    copy[date][employeeId] = {
      ...copy[date][employeeId],
      ...newShift
    }

    // Log activity for each changed shift
    const userInfo = getCurrentUserInfo(user, currentEmployee)
    for (const [shiftKey, shiftData] of Object.entries(newShift)) {
      const oldShift = oldShifts[shiftKey] || null
      await logShiftChanges(
        currentCompany.key,
        date,
        employeeId,
        oldShift,
        shiftData,
        shiftKey,
        userInfo.id,
        userInfo.name,
        userInfo.avatar
      )
    }

    setAttendanceData(copy)

    // Persist changes to Firebase database
    try {
      const updates: { [path: string]: any } = {}
      for (const [shiftKey, shiftData] of Object.entries(newShift)) {
        updates[
          `Attendance/${currentCompany.key}/${date}/${employeeId}/${shiftKey}`
        ] = shiftData
      }
      await database.ref().update(updates)
    } catch (error) {
      console.error('Failed to save shift to database:', error)
    }
  }

  // Delete shift function (ported from PayrollOld)
  const onDeleteShift = async (
    shiftKey: string,
    employeeId: string,
    date: string
  ) => {
    const copy = cloneDeep(attendanceData)

    if (copy[date] && copy[date][employeeId]) {
      // Get the shift data before deletion for activity logging
      const deletedShift = copy[date][employeeId][shiftKey]

      if (deletedShift) {
        // Log the deletion activity
        const userInfo = getCurrentUserInfo(user, currentEmployee)
        await logShiftDeletion(
          currentCompany.key,
          date,
          employeeId,
          deletedShift,
          shiftKey,
          userInfo.id,
          userInfo.name,
          userInfo.avatar
        )
      }

      delete copy[date][employeeId][shiftKey]

      // Persist deletion to Firebase database
      try {
        await database
          .ref(
            `Attendance/${currentCompany.key}/${date}/${employeeId}/${shiftKey}`
          )
          .remove()
      } catch (error) {
        console.error('Failed to delete shift from database:', error)
      }
    }

    setAttendanceData(copy)
  }

  const claimedAmount = 8114.76

  const [animationKey, setAnimationKey] = useState(0)
  const handleMouseEnter = () => {
    setAnimationKey(prev => prev + 1) // triggers rerender
  }

  // Error retry handler
  const handleRetry = () => {
    setDataError(null)
    setScheduleError(null)
    setRetryCount(0)
    // Trigger data reload by updating a dependency
    window.location.reload()
  }

  // Graceful degradation - determine if we should show degraded view
  const shouldShowDegradedView = (dataError || scheduleError) && retryCount >= maxRetries
  const hasPartialData = isDataLoaded || isScheduleLoaded

  return (
    <>
      {/* Error Display */}
      {(dataError || scheduleError) && (
        <ErrorContainer>
          <div className="error-content">
            <div className="error-icon">⚠️</div>
            <div className="error-details">
              <div className="error-title">
                {shouldShowDegradedView ? 'Running in Limited Mode' : 'Data Loading Error'}
              </div>
              {!isOnline && (
                <div className="error-message">
                  <strong>No internet connection</strong> - Please check your network
                </div>
              )}
              {dataError && (
                <div className="error-message">
                  Attendance: {dataError}
                  {!isOnline && ' (offline)'}
                </div>
              )}
              {scheduleError && (
                <div className="error-message">
                  Schedule: {scheduleError}
                  {!isOnline && ' (offline)'}
                </div>
              )}
              {isRetrying && (
                <div className="retry-info">
                  Retrying... (Attempt {retryCount}/{maxRetries})
                </div>
              )}
              {shouldShowDegradedView && hasPartialData && (
                <div className="degraded-info">
                  Some features may be limited. Showing available data.
                </div>
              )}
              {shouldShowDegradedView && !hasPartialData && (
                <div className="degraded-info">
                  Unable to load data. Please check your connection and try again.
                </div>
              )}
            </div>
            <button className="retry-button" onClick={handleRetry} disabled={isRetrying}>
              {isRetrying ? 'Retrying...' : shouldShowDegradedView ? 'Try Again' : 'Retry'}
            </button>
          </div>
        </ErrorContainer>
      )}

      {/* Loading Indicator */}
      {(!isDataLoaded || !isScheduleLoaded) && !dataError && !scheduleError && (
        <LoadingContainer>
          <div className="loading-content">
            <div className="loading-spinner"></div>
            <div className="loading-text">
              Loading payroll data...
              {isRetrying && ` (Retry ${retryCount}/${maxRetries})`}
            </div>
          </div>
        </LoadingContainer>
      )}

      <WeekBlockWrapStyled>
        <WeekBlockStyled>
          <WeekTopBlockStyled>
            <WeekTopBlockRowStyled>
              <PeriodBlockStyled ref={calendarRef}>
                <CalendarButtonStyled
                  onClick={() => setShowCalendar(!showCalendar)}
                >
                  <CalendarIconStyled
                    src={calendarIcon}
                    alt=''
                  />
                </CalendarButtonStyled>

                <PeriodSliderStyled>
                  
                  <PeriodArrowStyled
                    onClick={() => handlePeriodSelect(currentPeriodOffset - 1)}
                  >
                    <ArrowLeft />
                  </PeriodArrowStyled>
                  <PeriodTextStyled
                    onClick={() => setShowCalendar(!showCalendar)}
                    $isLowerCase={isLocaleFr}
                  >
                    {currentDisplayPeriod.startStr} -{' '}
                    {currentDisplayPeriod.endStr}
                  </PeriodTextStyled>
                    <PeriodArrowStyled
                    onClick={() => handlePeriodSelect(currentPeriodOffset + 1)}
                  >
                    <ArrowRight />
                  </PeriodArrowStyled>
                </PeriodSliderStyled>
                <PeriodStatusStyled
                  $isCurrentPeriod={!isPastPeriod && !isFuturePeriod}
                  onClick={() => setShowCalendar(!showCalendar)}
                >
                  {isPastPeriod
                    ? I18n.t('payroll.past_period')
                    : isFuturePeriod
                      ? I18n.t('payroll.upcoming')
                      : I18n.t('payroll.current_period')}
                </PeriodStatusStyled>
              </PeriodBlockStyled>

              {showCalendar && (
                <GreyOverlayStyled onClick={() => setShowCalendar(false)} />
              )}
              <Overlay
                rootClose
                show={showCalendar}
                placement='bottom'
                onHide={() => setShowCalendar(false)}
                target={() => calendarRef.current}
              >
                <CalendarPopover
                  onClose={() => setShowCalendar(false)}
                  startOfPeriod={startOfPeriod}
                  payrollLength={payrollLength}
                  currentPeriodOffset={currentPeriodOffset}
                  onPeriodSelect={handlePeriodSelect}
                  attendanceSettings={attendanceSettings}
                  payrollStartingDay={currentCompany.payrollStartingDay}
                  activeWeekPeriodTab={activeWeekPeriodTab}
                />
              </Overlay>

              <WeekPeriodTabsStyled role='tablist'>
                {weekPeriodTabs.map(tab => (
                  <TabButtonStyled
                    key={tab.id}
                    role='tab'
                    aria-selected={activeWeekPeriodTab === tab.id}
                    onClick={() => setActiveWeekPeriodTab(tab.id)}
                    $isActive={activeWeekPeriodTab === tab.id}
                  >
                    {tab.label}
                  </TabButtonStyled>
                ))}
              </WeekPeriodTabsStyled>
            </WeekTopBlockRowStyled>
            <WeekTopBlockRowStyled>
              {/* 
             Will be added at stage 2
              <ButtonStyled>
                <NoteIconStyled
                  src={noteIcon}
                  alt=''
                />
                {I18n.t('payroll.notes')}
                <span>0</span>
              </ButtonStyled>
               */}
              <ClaimedStatusStyled onMouseEnter={handleMouseEnter}>
                <SparkleIconStyled key={animationKey} />
                <ClaimedStatusBlockStyled>
                  <ClaimedTextStyled>
                    {I18n.t('payroll.you_saved')}
                  </ClaimedTextStyled>
                  <RollingNumber value={claimedAmount} />
                </ClaimedStatusBlockStyled>
              </ClaimedStatusStyled>

              <ButtonStyled
                $isOrange={numberOfConflicts > 0}
                onClick={() => setShowConflictModal(true)}
              >
                <ClockIconStyled />
                {I18n.t('payroll.conflicting_shifts')}
                <span>{numberOfConflicts}</span>
              </ButtonStyled>
            </WeekTopBlockRowStyled>
          </WeekTopBlockStyled>
          <WeekDaysStyled>
            {staticWeekDays.map((day, index) => (
              <DayStyled
                $isToday={index === currentDayIndex}
                key={index}
              >
                {day.displayText}
              </DayStyled>
            ))}
          </WeekDaysStyled>
        </WeekBlockStyled>
      </WeekBlockWrapStyled>

      <HoursTable
        employeesArray={employeesArray.map((employee, index) => ({
          id: index + 1,
          name: employee.name || '',
          surname: employee.surname || '',
          avatar: employee.avatar || '',
          userId: employee.userId || '',
          uid: employee.uid || '',
          positions: employee.positions || [],
          payrollId: employee.payrollId,
          customId: employee.customId
        }))}
        employeesByRole={employeesByRole}
        searchEmployee={searchTerm}
        onSearchEmployee={onSearchEmployee}
        displayBy={displayBy}
        setDisplayBy={setDisplayBy}
        displayByArray={displayByArray}
        attendanceData={filteredAttendanceData}
        currentCompany={currentCompany}
        isDataLoaded={isDataLoaded}
        selectedPositionId={selectedPositionId}
        setSelectedPositionId={_setSelectedPositionId}
        startOfPeriod={filteredPeriodData.startOfPeriod}
        currentPeriodOffset={currentPeriodOffset}
        payrollLength={filteredPeriodData.payrollLength}
        onSave={onSave}
        onDeleteShift={onDeleteShift}
        departmentRoles={departmentRoles}
        roleFilterState={roleFilterState}
        onRoleFilterChange={handleRoleFilterChange}
        isSingleRole={isSingleRole}
        singleRoleInfo={singleRoleInfo}
        hasPayrollIntegration={hasPayrollIntegration}
      />

      <PayrollConflictModal
        key={showConflictModal ? 'open' : 'closed'} // ✅ Force re-mount on open/close
        showModal={showConflictModal}
        onClose={() => setShowConflictModal(false)}
        conflicts={cloneDeep(allConflictingShifts)}
        roundingTime={15}
        companyId={currentCompany?.key || ''}
        jobs={currentCompany?.jobs || {}}
      />
      {/* TODO add new modal */}
      {/* <ConflictShiftModal
        show={showConflictModal}
        onHide={() => setShowConflictModal(false)}
      /> */}
    </>
  )
}

export default Hours

const TabListStyled = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  justify-self: center;

  padding: 0.3rem;
  gap: 0.3rem;

  border-radius: 0.8rem;
  background-color: rgba(229, 235, 239, 0.8);
`

const TabButtonStyled = styled.button<{
  $isActive: boolean
  $noOpacity?: boolean
}>`
  display: flex;
  align-items: center;
  justify-content: center;

  gap: 0.5rem;
  padding: 0.3rem 1rem;
  min-width: 7.5rem;

  border: none;
  background: ${({ $isActive }) =>
    $isActive ? 'linear-gradient(180deg, #3BBCFF, #2D87FF 100%)' : 'none'};
  border-radius: 0.6rem;
  box-shadow: ${({ $isActive }) =>
    $isActive
      ? '2px 2px 4px -1 rgba(18, 18, 23,0.06),2px 2px 4px -1 rgba(18, 18, 23,0.08)'
      : null};

  color: ${({ $isActive }) =>
    $isActive ? 'white' : theme.colorsNew.darkGrey500};
  font-size: 0.95rem;
  font-family: ${theme.fonts.normal};
  transition: all 0.2s ease-in-out;
  opacity: ${({ $isActive, $noOpacity }) =>
    $isActive || $noOpacity ? 1 : 0.3};

  &:hover,
  &:focus {
    opacity: 1;
  }

  svg {
    width: 1.2rem;
    height: 1.2rem;
    flex-shrink: 0;
    fill: currentColor;
  }
`

const WeekBlockWrapStyled = styled.div`
  width: 100%;
  padding: 1rem;
`

const WeekBlockStyled = styled.div`
  display: flex;
  align-items: center;
  flex-direction: column;

  gap: 0.8rem;
  width: 100%;
  padding: 1rem 1.5rem;

  border-radius: 1rem;
  background-color: #fff;
`

const WeekTopBlockStyled = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;

  width: 100%;
`

const WeekTopBlockRowStyled = styled.div`
  display: flex;
  align-items: center;

  gap: 0.3rem;
`

const PeriodBlockStyled = styled.div`
  display: flex;
  align-items: center;

  gap: 0.5rem;
  padding: 0 1rem;
`

const CalendarButtonStyled = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;

  width: 1.4rem;
  height: 1.4rem;
  padding: 0;

  color: ${theme.colorsNew.darkGrey500};

  border: 0;
  background-color: unset;
  opacity: 0.7;
  transition: all 0.2s ease-in-out;
  :hover,
  :focus {
    opacity: 1;
  }
`
const CalendarIconStyled = styled.img`
  width: 1.2rem;
  height: 1.2rem;
`

const PeriodSliderStyled = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;

  gap: 0.2rem;
`

const PeriodArrowStyled = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;

  width: 1.4rem;
  height: 1.4rem;
  padding: 0;

  border: none;
  border-radius: 50%;
  background-color: transparent;
  cursor: pointer;
  opacity: 0.7;

  transition: opacity 0.2s ease;

  :hover,
  :focus {
    opacity: 1;
  }

  svg {
    width: 1rem;
    height: 1rem;
  }
`


const PeriodTextStyled = styled.button<{ $isLowerCase?: boolean }>`
  padding: 0;
  min-width: 7rem;

  border: none;
  background-color: transparent;

  color: ${theme.colorsNew.darkGrey500};
  font-size: 0.9rem;
  font-family: ${theme.fonts.bold};
  text-transform: ${({ $isLowerCase }) => ($isLowerCase ? 'lowercase' : null)};
`

const PeriodStatusStyled = styled.button<{ $isCurrentPeriod: boolean }>`
  display: flex;
  align-items: center;
  justify-content: center;

  padding: 0.2rem 0.8rem;

  border: none;
  border-radius: 0.6rem;
  background-color: #e6f7ff;

  color: #00a2e9;
  font-size: 0.75rem;
  font-family: ${theme.fonts.normal};
  line-height: normal;
  transition: all 0.2s ease-in-out;
  :hover,
  :focus {
    color: #fff;
    background-color: #00a2e9;
  }
`

const WeekPeriodTabsStyled = styled(TabListStyled)`
  padding: 0;

  ${TabButtonStyled} {
    min-width: 5.5rem;
    font-size: 0.875rem;
  }
`

const ButtonStyled = styled.button<{ $isOrange?: boolean }>`
  display: flex;
  align-items: center;
  justify-content: center;

  gap: 0.4rem;
  height: 2rem;
  padding: 0.2rem 0.8rem;

  border: ${({ $isOrange }) => ($isOrange ? '0' : '1px solid #d0d5dd')};
  border-radius: 0.6rem;
  background: ${({ $isOrange }) =>
    $isOrange ? 'linear-gradient( #FFA100, #FF4D00)' : '#fff'};

  color: ${({ $isOrange }) =>
    $isOrange ? '#fff' : theme.colorsNew.darkGrey500};
  font-size: 0.8rem;
  font-family: ${theme.fonts.normal};

  span {
    display: flex;
    align-items: center;
    justify-content: center;

    width: 1rem;
    height: 1rem;

    border-radius: 50%;
    background-color: ${({ $isOrange }) => ($isOrange ? '#fff' : '#848da3')};

    color: ${({ $isOrange }) => ($isOrange ? '#E18700' : '#fff')};
    font-size: 0.675rem;
    font-family: ${theme.fonts.bold};
    line-height: normal;
  }

  :hover,
  :focus {
    background: ${({ $isOrange }) => ($isOrange ? '#FFA000' : '#d0d5dd')};
  }
`

// const NoteIconStyled = styled.img`
//   width: 1.2rem;
//   height: 1.2rem;
// `

const ClockIconStyled = styled(ClockIcon)`
  width: 1rem;
  height: 1rem;
  fill: currentColor;
`

const WeekDaysStyled = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;

  gap: 0.3rem;
  width: 100%;
`

const DayStyled = styled.div<{ $isToday: boolean }>`
  display: flex;
  align-items: center;
  justify-content: center;

  flex: 1;
  padding: 0.3rem;

  border: 1px solid
    ${({ $isToday }) => ($isToday ? theme.colorsNew.blue : 'transparent')};
  border-radius: 0.6rem;
  background-color: #efefef;

  color: ${theme.colorsNew.darkGrey500};
  font-size: 0.9rem;
  font-family: ${theme.fonts.normal};
`

const GreyOverlayStyled = styled.div`
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
`

const ClaimedStatusStyled = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;

  gap: 0.5rem;
  height: 2rem;
  padding: 0.2rem 0.2rem 0.2rem 0.6rem;

  border-radius: 0.6rem;
  background: linear-gradient(
    90deg,
    #01d9d5 0%,
    #00befa 18.27%,
    #9967ff 42.79%,
    #fd5c61 61.54%,
    #ff902d 84.13%,
    #ffa000 97.12%
  );
`

const ClaimedStatusBlockStyled = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;

  gap: 0.4rem;
  height: 100%;
  padding: 0.1rem 0.6rem;

  border-radius: 0.4rem;
  background-color: #fff;
  overflow: hidden;
`

const ClaimedTextStyled = styled.p`
  color: ${theme.colorsNew.darkGrey500};
  font-size: 0.8rem;
  font-family: ${theme.fonts.light};
  line-height: normal;
`

const SparkleIconStyled = styled(SparkleIcon)`
  width: 1.2rem;
  height: 1.2rem;
  fill: currentColor;

  .sparkleStar1 {
    transform-origin: center;
    animation: sparkle 0.8s ease-in-out forwards;
  }
  .sparkleStar2 {
    transform-origin: right;
    animation: sparkle2 1s ease-in-out 0.4s forwards;
  }
  .sparkleStar3 {
    transform-origin: top;
    animation: sparkle3 1.3s ease-in-out forwards;
  }

  @keyframes sparkle {
    0% {
      transform: scale(0.5);
    }
    100% {
      transform: scale(1);
    }
  }
  @keyframes sparkle2 {
    0% {
      transform: scale(0.8);
    }
    25% {
      transform: scale(1.2);
    }
    40% {
      transform: scale(1.4);
    }
    65% {
      transform: scale(1.2);
    }
    100% {
      transform: scale(1);
    }
  }
  @keyframes sparkle3 {
    0% {
      transform: scale(1);
    }
    25% {
      transform: scale(1.4);
    }
    40% {
      transform: scale(1.8);
    }
    50% {
      transform: scale(1.4);
    }
    60% {
      transform: scale(1.1);
    }
    100% {
      transform: scale(1);
    }
  }
`

// Error Container Styled Component
const ErrorContainer = styled.div`
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
  background: #fff;
  border: 1px solid #ff6b6b;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(255, 107, 107, 0.15);
  max-width: 400px;
  animation: slideInRight 0.3s ease-out;

  .error-content {
    display: flex;
    align-items: flex-start;
    padding: 16px;
    gap: 12px;
  }

  .error-icon {
    font-size: 24px;
    flex-shrink: 0;
  }

  .error-details {
    flex: 1;
  }

  .error-title {
    font-weight: 600;
    color: #d63031;
    margin-bottom: 8px;
  }

  .error-message {
    color: #636e72;
    font-size: 14px;
    margin-bottom: 4px;
  }

  .retry-info {
    color: #0984e3;
    font-size: 12px;
    font-style: italic;
  }

  .degraded-info {
    color: #f39c12;
    font-size: 12px;
    font-weight: 500;
    margin-top: 4px;
  }

  .retry-button {
    background: #d63031;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 8px 16px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.2s;

    &:hover:not(:disabled) {
      background: #b71c1c;
    }

    &:disabled {
      background: #ddd;
      cursor: not-allowed;
    }
  }

  @keyframes slideInRight {
    from {
      transform: translateX(100%);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }
`

// Loading Container Styled Component
const LoadingContainer = styled.div`
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 999;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);

  .loading-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 32px;
    gap: 16px;
  }

  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #0984e3;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  .loading-text {
    color: #636e72;
    font-size: 16px;
    text-align: center;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`
