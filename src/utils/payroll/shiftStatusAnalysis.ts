import dayjs from 'dayjs'

import { analyzeShiftConflicts } from './shiftConflictUtils'

import { checkShiftOverlap } from 'utils/attendance'

import { AttendanceShift, AttendanceShifts } from 'types/attendance'
import { Company } from 'types/company'

// Constants for validation thresholds
const MINIMUM_SHIFT_DURATION_MINUTES = 30 // Shifts shorter than 30 minutes are considered too short
const MINIMUM_WAGE_HOURS = 3 // Legal minimum of 3 hours payment
const BREAK_TOLERANCE_MINUTES = 15 // Tolerance for break duration compliance

export type ShiftStatus =
  | 'current' // Currently punched in (blue)
  | 'confirmed' // Normal confirmed shift (green/default)
  | 'conflicting-start' // Early arrival (orange)
  | 'conflicting-end' // Late departure (orange)
  | 'missing-end' // Forgot to punch out (red)
  | 'overlapping' // Shift overlap error (red)
  | 'too-short' // Shift too short (yellow)
  | 'minimum-wage' // Less than 3 hours (purple)
  | 'break-unpaid-issue' // Unpaid break compliance issue (yellow)
  | 'break-paid-issue' // Paid break compliance issue (yellow)

export type ShiftAnalysis = {
  status: ShiftStatus
  priority: number // Higher number = higher priority for display
  issues: string[] // List of specific issues found
  breakIssues?: {
    unpaidBreakIssues: string[]
    paidBreakIssues: string[]
  }
}

/**
 * Analyzes a shift for all user story scenarios and returns comprehensive status
 */
export const analyzeShiftStatus = (
  shift: AttendanceShift,
  employeeId: string,
  date: string,
  attendanceData: AttendanceShifts,
  currentCompany: Company,
  isToday: boolean = false
): ShiftAnalysis => {
  const issues: string[] = []
  const breakIssues = {
    unpaidBreakIssues: [] as string[],
    paidBreakIssues: [] as string[]
  }

  // 1. Current shift: Employee currently punched in
  const isCurrentlyWorking = shift.start && !shift.end
  if (isCurrentlyWorking) {
    return {
      status: 'current',
      priority: 10,
      issues: ['Employee is currently punched in'],
      breakIssues
    }
  }

  // 7. Missing end time: Past shifts without end time
  if (shift.start && !shift.end && !isToday) {
    return {
      status: 'missing-end',
      priority: 9,
      issues: ['Employee forgot to punch out'],
      breakIssues
    }
  }

  // Enhanced shift duration validation
  const durationAnalysis = validateShiftDuration(shift)
  if (durationAnalysis.hasIssues) {
    issues.push(...durationAnalysis.issues)

    // Return early for critical duration issues
    if (durationAnalysis.status === 'minimum-wage') {
      return {
        status: 'minimum-wage',
        priority: 8,
        issues,
        breakIssues
      }
    }

    if (durationAnalysis.status === 'too-short') {
      return {
        status: 'too-short',
        priority: 7,
        issues,
        breakIssues
      }
    }
  }

  // 8. Overlapping shifts detection
  const overlapAnalysis = analyzeShiftOverlaps(
    shift,
    employeeId,
    date,
    attendanceData
  )
  if (overlapAnalysis.hasOverlap) {
    return {
      status: 'overlapping',
      priority: 9,
      issues: [...issues, ...overlapAnalysis.overlapIssues],
      breakIssues
    }
  }

  // Analyze break compliance (3. Unpaid Break & 4. Paid Break)
  analyzeBreakCompliance(shift, currentCompany, breakIssues)

  // 5. Conflicting start time & 6. Conflicting end time
  if (shift.start && shift.end) {
    const shiftStartRounded = Math.round(shift.start / 5) * 5 // Round to nearest 5 minutes
    const shiftEndRounded = Math.round(shift.end / 5) * 5

    // Use existing conflict analysis
    const conflictAnalysis = analyzeShiftConflicts(
      shift,
      shiftStartRounded,
      shiftEndRounded,
      null, // scheduledShift - would need to be passed in for full analysis
      isToday,
      false
    )

    if (conflictAnalysis.isClockInDifferent) {
      issues.push('Employee arrived early - confirm authorization')
      return {
        status: 'conflicting-start',
        priority: 6,
        issues,
        breakIssues
      }
    }

    if (conflictAnalysis.isClockOutDifferent) {
      issues.push('Employee left later than scheduled')
      return {
        status: 'conflicting-end',
        priority: 6,
        issues,
        breakIssues
      }
    }
  }

  // Check for break issues
  if (
    breakIssues.unpaidBreakIssues.length > 0 ||
    breakIssues.paidBreakIssues.length > 0
  ) {
    const breakStatus =
      breakIssues.unpaidBreakIssues.length > 0
        ? 'break-unpaid-issue'
        : 'break-paid-issue'
    return {
      status: breakStatus,
      priority: 5,
      issues: [
        ...issues,
        ...breakIssues.unpaidBreakIssues,
        ...breakIssues.paidBreakIssues
      ],
      breakIssues
    }
  }

  // 2. Confirmed: Normal confirmed shift (default case)
  return {
    status: 'confirmed',
    priority: 1,
    issues: shift.isConfirmed ? [] : ['Shift not yet confirmed'],
    breakIssues
  }
}

/**
 * Analyzes break compliance for both paid and unpaid breaks
 */
const analyzeBreakCompliance = (
  shift: AttendanceShift,
  currentCompany: Company,
  breakIssues: { unpaidBreakIssues: string[]; paidBreakIssues: string[] }
) => {
  if (!shift.breaks || !currentCompany.breaks) return

  const actualBreaks = Object.values(shift.breaks)
  const companyBreaks = currentCompany.breaks

  // Calculate total shift duration in hours
  const shiftDurationHours =
    shift.start && shift.end ? (shift.end - shift.start) / 60 : 0

  // Check each company break policy
  Object.entries(companyBreaks).forEach(([breakId, breakPolicy]) => {
    // Check if employee is entitled to this break based on shift duration
    if (shiftDurationHours >= breakPolicy.hours) {
      const expectedBreakDuration = breakPolicy.length * 60 // Convert to minutes

      // Find actual break taken that matches this policy
      const matchingBreak = findMatchingBreak(
        actualBreaks,
        expectedBreakDuration
      )

      if (!matchingBreak) {
        // Determine if break is paid or unpaid based on company settings
        const isUnpaidBreak = shouldBreakBeUnpaid(breakPolicy, currentCompany)
        const breakType = isUnpaidBreak ? 'unpaid' : 'paid'
        const issueArray = isUnpaidBreak
          ? breakIssues.unpaidBreakIssues
          : breakIssues.paidBreakIssues

        issueArray.push(
          `Missing ${breakPolicy.length}-minute ${breakType} break for ${breakPolicy.hours}+ hour shift`
        )
      } else {
        validateBreakDuration(
          matchingBreak,
          expectedBreakDuration,
          breakPolicy,
          currentCompany,
          breakIssues
        )
      }
    }
  })

  // Check for unauthorized breaks (breaks taken that don't match any policy)
  checkUnauthorizedBreaks(
    actualBreaks,
    companyBreaks,
    shiftDurationHours,
    breakIssues
  )
}

/**
 * Finds a break that matches the expected duration within tolerance
 */
const findMatchingBreak = (actualBreaks: any[], expectedDuration: number) => {
  return actualBreaks.find(breakItem => {
    if (!breakItem.start || !breakItem.end) return false
    const actualDuration = breakItem.end - breakItem.start
    return (
      Math.abs(actualDuration - expectedDuration) <= BREAK_TOLERANCE_MINUTES
    )
  })
}

/**
 * Determines if a break should be unpaid based on company policy
 */
const shouldBreakBeUnpaid = (
  breakPolicy: any,
  currentCompany: Company
): boolean => {
  // Default logic: breaks longer than 30 minutes are typically unpaid
  // This can be enhanced based on company-specific break policies
  return breakPolicy.length >= 30 || currentCompany.excludeBreaks === true
}

/**
 * Validates the duration of a taken break against policy
 */
const validateBreakDuration = (
  actualBreak: any,
  expectedDuration: number,
  breakPolicy: any,
  currentCompany: Company,
  breakIssues: { unpaidBreakIssues: string[]; paidBreakIssues: string[] }
) => {
  const actualDuration = actualBreak.end - actualBreak.start
  const durationDiff = Math.abs(actualDuration - expectedDuration)

  if (durationDiff > BREAK_TOLERANCE_MINUTES) {
    const isUnpaidBreak = shouldBreakBeUnpaid(breakPolicy, currentCompany)
    const breakType = isUnpaidBreak ? 'unpaid' : 'paid'
    const issueArray = isUnpaidBreak
      ? breakIssues.unpaidBreakIssues
      : breakIssues.paidBreakIssues

    const status = actualDuration > expectedDuration ? 'longer' : 'shorter'
    const actualMinutes = Math.round(actualDuration)
    const expectedMinutes = Math.round(expectedDuration)

    issueArray.push(
      `${breakType} break duration ${status} than expected (${actualMinutes} vs ${expectedMinutes} minutes)`
    )
  }
}

/**
 * Checks for breaks taken that don't match any company policy
 */
const checkUnauthorizedBreaks = (
  actualBreaks: any[],
  companyBreaks: any,
  shiftDurationHours: number,
  breakIssues: { unpaidBreakIssues: string[]; paidBreakIssues: string[] }
) => {
  const authorizedBreakDurations = Object.values(companyBreaks).map(
    (policy: any) => policy.length * 60
  )

  actualBreaks.forEach(breakItem => {
    if (!breakItem.start || !breakItem.end) return

    const actualDuration = breakItem.end - breakItem.start
    const isAuthorized = authorizedBreakDurations.some(
      duration => Math.abs(actualDuration - duration) <= BREAK_TOLERANCE_MINUTES
    )

    if (!isAuthorized && actualDuration > 5) {
      // Ignore very short breaks (< 5 minutes)
      const minutes = Math.round(actualDuration)
      breakIssues.unpaidBreakIssues.push(
        `Unauthorized ${minutes}-minute break taken`
      )
    }
  })
}

/**
 * Gets the appropriate color for a shift status
 */
export const getShiftStatusColor = (status: ShiftStatus): string => {
  switch (status) {
    case 'current':
      return 'blue'
    case 'confirmed':
      return 'default'
    case 'conflicting-start':
    case 'conflicting-end':
      return 'orange'
    case 'missing-end':
    case 'overlapping':
      return 'red'
    case 'too-short':
    case 'break-unpaid-issue':
    case 'break-paid-issue':
      return 'yellow'
    case 'minimum-wage':
      return 'purple'
    default:
      return 'default'
  }
}

/**
 * Validates shift duration and identifies issues
 */
const validateShiftDuration = (
  shift: AttendanceShift
): {
  hasIssues: boolean
  status: 'minimum-wage' | 'too-short' | 'normal'
  issues: string[]
} => {
  const issues: string[] = []

  if (!shift.start || !shift.end) {
    return { hasIssues: false, status: 'normal', issues }
  }

  const shiftDuration = shift.end - shift.start
  const shiftHours = shiftDuration / 60

  // Check for minimum wage compliance (highest priority)
  if (shiftDuration > 0 && shiftHours < MINIMUM_WAGE_HOURS) {
    const actualHours = Math.round(shiftHours * 100) / 100
    issues.push(
      `Shift duration: ${actualHours} hours (minimum ${MINIMUM_WAGE_HOURS} hours required by law)`
    )
    issues.push(
      'Employee must be paid for minimum 3 hours regardless of actual time worked'
    )
    return { hasIssues: true, status: 'minimum-wage', issues }
  }

  // Check for shifts that are too short (but above minimum wage threshold)
  if (shiftDuration > 0 && shiftDuration < MINIMUM_SHIFT_DURATION_MINUTES) {
    const actualMinutes = Math.round(shiftDuration)
    issues.push(
      `Shift too short: ${actualMinutes} minutes (minimum ${MINIMUM_SHIFT_DURATION_MINUTES} minutes recommended)`
    )
    issues.push('Verify employee did not forget to punch in initially')
    return { hasIssues: true, status: 'too-short', issues }
  }

  return { hasIssues: false, status: 'normal', issues }
}

/**
 * Analyzes shift overlaps with detailed information
 */
const analyzeShiftOverlaps = (
  shift: AttendanceShift,
  employeeId: string,
  date: string,
  attendanceData: AttendanceShifts
): { hasOverlap: boolean; overlapIssues: string[] } => {
  const dayShifts = attendanceData[date]?.[employeeId] || {}
  const previousDate = dayjs(date).subtract(1, 'day').format('YYYY-MM-DD')
  const nextDate = dayjs(date).add(1, 'day').format('YYYY-MM-DD')

  const previousDayShifts = attendanceData[previousDate]?.[employeeId] || {}
  const nextDayShifts = attendanceData[nextDate]?.[employeeId] || {}

  const overlappingShifts = checkShiftOverlap({
    employeeShifts: dayShifts,
    previousDayShifts,
    nextDayShifts
  })

  // Find current shift key to check for overlaps
  const currentShiftKey = Object.keys(dayShifts).find(
    key => dayShifts[key] === shift
  )

  const overlapIssues: string[] = []
  let hasOverlap = false

  if (currentShiftKey && overlappingShifts[currentShiftKey]) {
    const overlap = overlappingShifts[currentShiftKey]

    if (overlap.isStartOverlapping) {
      hasOverlap = true
      overlapIssues.push('Shift start overlaps with previous shift')
    }

    if (overlap.isEndOverlapping) {
      hasOverlap = true
      overlapIssues.push('Shift end overlaps with next shift')
    }
  }

  // Additional check for same-day overlaps
  const currentShiftStart = shift.start
  const currentShiftEnd = shift.end

  if (currentShiftStart && currentShiftEnd) {
    Object.entries(dayShifts).forEach(([shiftKey, otherShift]) => {
      if (shiftKey !== currentShiftKey && otherShift.start && otherShift.end) {
        // Check if shifts overlap on the same day
        const otherStart = otherShift.start
        const otherEnd = otherShift.end

        const overlaps =
          currentShiftStart < otherEnd && currentShiftEnd > otherStart

        if (overlaps) {
          hasOverlap = true
          const startTime =
            Math.floor(otherStart / 60)
              .toString()
              .padStart(2, '0') +
            ':' +
            (otherStart % 60).toString().padStart(2, '0')
          const endTime =
            Math.floor(otherEnd / 60)
              .toString()
              .padStart(2, '0') +
            ':' +
            (otherEnd % 60).toString().padStart(2, '0')
          overlapIssues.push(`Overlaps with shift ${startTime}-${endTime}`)
        }
      }
    })
  }

  return { hasOverlap, overlapIssues }
}

/**
 * Gets user-friendly description for shift status
 */
export const getShiftStatusDescription = (status: ShiftStatus): string => {
  switch (status) {
    case 'current':
      return 'Currently working'
    case 'confirmed':
      return 'Confirmed shift'
    case 'conflicting-start':
      return 'Early arrival'
    case 'conflicting-end':
      return 'Late departure'
    case 'missing-end':
      return 'Missing punch out'
    case 'overlapping':
      return 'Overlapping shifts'
    case 'too-short':
      return 'Shift too short'
    case 'minimum-wage':
      return 'Minimum wage compliance'
    case 'break-unpaid-issue':
      return 'Unpaid break issue'
    case 'break-paid-issue':
      return 'Paid break issue'
    default:
      return 'Unknown status'
  }
}
