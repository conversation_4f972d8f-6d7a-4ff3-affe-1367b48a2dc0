import dayjs from 'dayjs'
import firebase from 'firebase/app'

import { SalaryRateUnit } from 'utils/constants'

import { AttendanceShift, AttendanceShifts } from 'types/attendance'

/**
 * Test data generator for attendance shifts with various conflict scenarios
 * This creates comprehensive test data to validate the ShiftPopover conflict analysis
 */

// Test employee IDs (these should exist in your test database)
const TEST_EMPLOYEES = [
  'test-employee-1',
  'test-employee-2',
  'test-employee-3',
  'test-employee-4',
  'test-employee-5'
]

// Test position IDs (these should exist in your test database)
const TEST_POSITIONS = [
  'test-position-server',
  'test-position-cook',
  'test-position-manager',
  'test-position-host'
]

// Test company ID (use your test company)
const TEST_COMPANY_ID = 'test-company-id'

/**
 * Generate test shifts for various conflict scenarios
 */
export const generateTestAttendanceData = async (
  companyId: string = TEST_COMPANY_ID
) => {
  const today = dayjs().format('YYYY-MM-DD')
  const tomorrow = dayjs().add(1, 'day').format('YYYY-MM-DD')
  const yesterday = dayjs().subtract(1, 'day').format('YYYY-MM-DD')

  const testData: AttendanceShifts = {}

  // Initialize dates
  testData[yesterday] = {}
  testData[today] = {}
  testData[tomorrow] = {}

  // SCENARIO 1: Overlapping shifts (RED conflicts)
  // Employee 1 has two overlapping shifts on the same day
  testData[today]['test-employee-1'] = {
    'shift-1': createShift({
      start: 480, // 8:00 AM
      end: 720, // 12:00 PM
      positionId: TEST_POSITIONS[0],
      rate: 15.5,
      isConfirmed: false
    }),
    'shift-2': createShift({
      start: 660, // 11:00 AM (overlaps with shift-1)
      end: 900, // 3:00 PM
      positionId: TEST_POSITIONS[1],
      rate: 16.0,
      isConfirmed: false
    })
  }

  // SCENARIO 2: Early arrival (ORANGE conflicts)
  // Employee 2 clocked in early, needs reclaim/approve decision
  testData[today]['test-employee-2'] = {
    'shift-1': createShift({
      start: 450, // 7:30 AM (30 min early)
      end: 720, // 12:00 PM
      positionId: TEST_POSITIONS[0],
      rate: 15.5,
      isConfirmed: false,
      scheduledStart: 480, // Was scheduled for 8:00 AM
      scheduledEnd: 720
    })
  }

  // SCENARIO 3: Missing role selection (GREY conflicts)
  // Employee 3 has shifts without position assigned
  testData[today]['test-employee-3'] = {
    'shift-1': createShift({
      start: 480, // 8:00 AM
      end: 720, // 12:00 PM
      positionId: undefined, // Missing role!
      rate: 15.5,
      isConfirmed: false
    }),
    'shift-2': createShift({
      start: 780, // 1:00 PM
      end: 1020, // 5:00 PM
      positionId: TEST_POSITIONS[1],
      rate: 16.0,
      isConfirmed: false
    })
  }

  // SCENARIO 4: Missing clock out (ORANGE conflicts)
  // Employee 4 didn't clock out
  testData[today]['test-employee-4'] = {
    'shift-1': createShift({
      start: 480, // 8:00 AM
      end: undefined, // Missing clock out!
      positionId: TEST_POSITIONS[2],
      rate: 18.0,
      isConfirmed: false
    })
  }

  // SCENARIO 5: Short shift (ORANGE conflicts)
  // Employee 5 has a very short shift (less than 3 hours)
  testData[today]['test-employee-5'] = {
    'shift-1': createShift({
      start: 480, // 8:00 AM
      end: 600, // 10:00 AM (only 2 hours)
      positionId: TEST_POSITIONS[0],
      rate: 15.5,
      isConfirmed: false
    })
  }

  // SCENARIO 6: Multiple conflicts on same employee
  // Employee 1 also has issues on tomorrow
  testData[tomorrow]['test-employee-1'] = {
    'shift-1': createShift({
      start: 480, // 8:00 AM
      end: 720, // 12:00 PM
      positionId: undefined, // Missing role
      rate: 15.5,
      isConfirmed: false
    }),
    'shift-2': createShift({
      start: 700, // 11:40 AM (overlaps with shift-1)
      end: 960, // 4:00 PM
      positionId: TEST_POSITIONS[1],
      rate: 16.0,
      isConfirmed: false
    }),
    'shift-3': createShift({
      start: 1020, // 5:00 PM
      end: 1080, // 6:00 PM (only 1 hour - very short)
      positionId: TEST_POSITIONS[2],
      rate: 18.0,
      isConfirmed: false
    })
  }

  // SCENARIO 7: Long shift (ORANGE conflicts)
  // Employee 2 has a very long shift (over 12 hours)
  testData[tomorrow]['test-employee-2'] = {
    'shift-1': createShift({
      start: 360, // 6:00 AM
      end: 1140, // 7:00 PM (13 hours)
      positionId: TEST_POSITIONS[0],
      rate: 15.5,
      isConfirmed: false
    })
  }

  // SCENARIO 8: Perfect shifts (GREEN status)
  // Employee 3 has properly configured shifts
  testData[tomorrow]['test-employee-3'] = {
    'shift-1': createShift({
      start: 480, // 8:00 AM
      end: 720, // 12:00 PM
      positionId: TEST_POSITIONS[0],
      rate: 15.5,
      isConfirmed: false
    }),
    'shift-2': createShift({
      start: 780, // 1:00 PM
      end: 1020, // 5:00 PM
      positionId: TEST_POSITIONS[1],
      rate: 16.0,
      isConfirmed: false
    })
  }

  // SCENARIO 9: Cross-day overlap
  // Employee 4 has a late shift that might overlap with next day
  testData[today]['test-employee-4'] = {
    ...testData[today]['test-employee-4'],
    'shift-2': createShift({
      start: 1320, // 10:00 PM
      end: 120, // 2:00 AM next day (cross-midnight)
      positionId: TEST_POSITIONS[3],
      rate: 17.0,
      isConfirmed: false
    })
  }

  // SCENARIO 10: Late departure (ORANGE conflicts)
  // Employee 5 stayed late
  testData[tomorrow]['test-employee-5'] = {
    'shift-1': createShift({
      start: 480, // 8:00 AM
      end: 750, // 12:30 PM (30 min late)
      positionId: TEST_POSITIONS[0],
      rate: 15.5,
      isConfirmed: false,
      scheduledStart: 480,
      scheduledEnd: 720 // Was scheduled until 12:00 PM
    })
  }

  return testData
}

/**
 * Helper function to create a shift with default values
 */
function createShift(options: {
  start?: number
  end?: number
  positionId?: string
  rate?: number
  isConfirmed?: boolean
  scheduledStart?: number
  scheduledEnd?: number
}): AttendanceShift {
  const shift: AttendanceShift = {
    start: options.start,
    end: options.end,
    positionId: options.positionId,
    rate: options.rate || 15.0,
    type: SalaryRateUnit.HOURLY,
    isConfirmed: options.isConfirmed || false,
    manuallyCreated: true,
    breaks: {},
    clockInSource: 'manual',
    clockOutSource: options.end ? 'manual' : undefined
  }

  // Add scheduled shift data if provided
  if (options.scheduledStart && options.scheduledEnd) {
    ;(shift as any).scheduledShift = {
      start: options.scheduledStart,
      end: options.scheduledEnd,
      uid: 'scheduled-shift-' + Math.random().toString(36).substr(2, 9),
      positionId: options.positionId || '',
      subcategoryId: 'test-subcategory'
    }
  }

  return shift
}

/**
 * Save test data to Firebase
 */
export const saveTestAttendanceData = async (
  companyId: string = TEST_COMPANY_ID
) => {
  try {
    const testData = await generateTestAttendanceData(companyId)
    const updates: { [path: string]: any } = {}

    // Prepare Firebase updates
    Object.entries(testData).forEach(([date, employeeData]) => {
      Object.entries(employeeData).forEach(([employeeId, shifts]) => {
        Object.entries(shifts).forEach(([shiftKey, shiftData]) => {
          updates[`Attendance/${companyId}/${date}/${employeeId}/${shiftKey}`] =
            shiftData
        })
      })
    })

    // Save to Firebase
    await firebase.database().ref().update(updates)

    console.log('✅ Test attendance data saved successfully!')
    console.log('📊 Created test scenarios:')
    console.log('  🔴 Overlapping shifts (Employee 1)')
    console.log('  🟠 Early arrivals (Employee 2)')
    console.log('  ⚫ Missing roles (Employee 3)')
    console.log('  🟠 Missing clock outs (Employee 4)')
    console.log('  🟠 Short shifts (Employee 5)')
    console.log('  🟢 Perfect shifts (Employee 3 tomorrow)')
    console.log('  🟠 Long shifts (Employee 2 tomorrow)')
    console.log('  🔴 Multiple conflicts (Employee 1 tomorrow)')

    return testData
  } catch (error) {
    console.error('❌ Failed to save test data:', error)
    throw error
  }
}

/**
 * Clear test data from Firebase
 */
export const clearTestAttendanceData = async (
  companyId: string = TEST_COMPANY_ID
) => {
  try {
    const dates = [
      dayjs().subtract(1, 'day').format('YYYY-MM-DD'),
      dayjs().format('YYYY-MM-DD'),
      dayjs().add(1, 'day').format('YYYY-MM-DD')
    ]

    const updates: { [path: string]: any } = {}

    dates.forEach(date => {
      TEST_EMPLOYEES.forEach(employeeId => {
        updates[`Attendance/${companyId}/${date}/${employeeId}`] = null
      })
    })

    await firebase.database().ref().update(updates)
    console.log('🧹 Test attendance data cleared successfully!')
  } catch (error) {
    console.error('❌ Failed to clear test data:', error)
    throw error
  }
}
