/**
 * Test Data Utilities
 * 
 * This module provides utilities to generate test data for various scenarios.
 * You can use these functions from the browser console to create test data.
 */

import { saveTestAttendanceData, clearTestAttendanceData, generateTestAttendanceData } from './attendanceTestDataGenerator'

// Make functions available globally for console access
declare global {
  interface Window {
    testData: {
      generateAttendanceData: typeof generateTestAttendanceData
      saveAttendanceData: typeof saveTestAttendanceData
      clearAttendanceData: typeof clearTestAttendanceData
    }
  }
}

// Attach to window for console access
if (typeof window !== 'undefined') {
  window.testData = {
    generateAttendanceData: generateTestAttendanceData,
    saveAttendanceData: saveTestAttendanceData,
    clearAttendanceData: clearTestAttendanceData
  }
}

export {
  generateTestAttendanceData,
  saveTestAttendanceData,
  clearTestAttendanceData
}

/**
 * Usage Instructions:
 * 
 * 1. Open browser console in your Pivot app
 * 2. Run one of these commands:
 * 
 * // Generate and save test data with your company ID
 * await window.testData.saveAttendanceData('your-company-id')
 * 
 * // Clear test data
 * await window.testData.clearAttendanceData('your-company-id')
 * 
 * // Just generate data without saving (for preview)
 * const data = await window.testData.generateAttendanceData('your-company-id')
 * console.log(data)
 */
